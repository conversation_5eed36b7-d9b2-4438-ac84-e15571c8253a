/**
 * <PERSON><PERSON><PERSON> to check what data is currently in the database
 */

const { MongoClient } = require('mongodb');

// Read .env file manually
const fs = require('fs');
const path = require('path');

try {
  const envFile = fs.readFileSync(path.join(__dirname, '.env'), 'utf8');
  const envLines = envFile.split('\n');
  for (const line of envLines) {
    if (line.includes('DATABASE_URL=')) {
      let url = line.split('=')[1].trim();
      // Remove quotes if present
      if (url.startsWith('"') && url.endsWith('"')) {
        url = url.slice(1, -1);
      }
      process.env.DATABASE_URL = url;
      break;
    }
  }
} catch (error) {
  console.error('Could not read .env file:', error.message);
}

async function checkDatabase() {
  console.log('Checking database status...');
  console.log('DATABASE_URL:', process.env.DATABASE_URL);

  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL not found in environment');
    return;
  }

  const client = new MongoClient(process.env.DATABASE_URL);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const dbName = process.env.DATABASE_URL.split('/').pop().split('?')[0];
    const db = client.db(dbName);
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('\nCollections found:');
    
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`- ${collection.name}: ${count} documents`);
      
      // Show sample data for important collections
      if (['User', 'App', 'LicenseRequest', 'Plugin'].includes(collection.name) && count > 0) {
        const sample = await db.collection(collection.name).findOne();
        console.log(`  Sample document:`, JSON.stringify(sample, null, 2));
      }
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await client.close();
  }
}

checkDatabase().catch(console.error);
