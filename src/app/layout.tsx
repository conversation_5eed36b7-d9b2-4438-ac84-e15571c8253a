import './globals.css';
import { SessionProvider } from 'next-auth/react';
import { Toaster } from 'sonner';
import { Toaster as HotToaster } from 'react-hot-toast';
import SessionMonitor from '@/components/SessionMonitor';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AveHub — Open Source & Custom Projects',
  description: 'Discover free open-source projects or buy custom-built websites, apps, bots, and more — powered by AveHub.',
  metadataBase: new URL('https://avehubs.com'),
  openGraph: {
    title: 'AveHub — Open Source & Custom Projects',
    description: 'Discover free open-source projects or buy custom-built websites, apps, bots, and more — powered by AveHub.',
    url: 'https://avehubs.com',
    siteName: 'AveHub',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'AveHub — Open Source & Custom Projects',
      },
    ],
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AveHub — Open Source & Custom Projects',
    description: 'Explore free open-source tools and order custom projects built to your needs.',
    images: ['/logo.png'],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="bg-gray-100 dark:bg-gray-900" suppressHydrationWarning={true}>
        <SessionProvider>
          <SessionMonitor>
            <main>{children}</main>
          </SessionMonitor>
        </SessionProvider>
        <Toaster richColors position="top-right" />
        <HotToaster position="top-right" />
      </body>
    </html>
  );
}
