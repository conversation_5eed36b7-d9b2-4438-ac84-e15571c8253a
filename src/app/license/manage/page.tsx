'use client';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import LoadingDots from '@/components/animations/Loading';
import { useLicenseManagement } from '@/hooks/license/useLicenseManagement';
import LicenseOverview from '../components/LicenseOverview';
import ServerManagement from '../components/ServerManagement';
import ApiKeyManagement from '../components/ApiKeyManagement';
import { Shield, Server, Key, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function LicenseManagePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { licenses, loading, error, refetch } = useLicenseManagement();
  const [activeTab, setActiveTab] = useState<'overview' | 'servers' | 'keys'>('overview');

  // Redirect to login if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <LoadingDots />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-red-900/20 border border-red-600 rounded-lg p-6">
              <h2 className="text-red-400 font-semibold mb-2">Error Loading Licenses</h2>
              <p className="text-gray-300">{error}</p>
              <button 
                onClick={refetch}
                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has any approved licenses
  const hasActiveLicense = licenses.some(license => license.isActive);

  if (!hasActiveLicense) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Link 
                href="/license" 
                className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors mb-4"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back to License Application
              </Link>
            </div>
            
            <div className="bg-yellow-900/20 border border-yellow-600 rounded-lg p-8 text-center">
              <Shield className="mx-auto text-yellow-500 mb-4" size={48} />
              <h2 className="text-yellow-400 font-semibold text-xl mb-2">No Active Licenses Found</h2>
              <p className="text-gray-300 mb-4">
                You don't have any active licenses yet. Please apply for a license first.
              </p>
              <Link 
                href="/license"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Apply for License
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Shield },
    { id: 'servers', label: 'Server Management', icon: Server },
    { id: 'keys', label: 'API Keys', icon: Key },
  ] as const;

  return (
    <div className="min-h-screen bg-gray-900">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">License Management</h1>
                <p className="text-gray-400">Manage your server licenses and API keys</p>
              </div>
              <Link 
                href="/license" 
                className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back to License Application
              </Link>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="border-b border-gray-700">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-400'
                          : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                      }`}
                    >
                      <Icon size={16} className="mr-2" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === 'overview' && (
              <LicenseOverview licenses={licenses} onRefetch={refetch} />
            )}
            {activeTab === 'servers' && (
              <ServerManagement licenses={licenses} onRefetch={refetch} />
            )}
            {activeTab === 'keys' && (
              <ApiKeyManagement licenses={licenses} onRefetch={refetch} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
