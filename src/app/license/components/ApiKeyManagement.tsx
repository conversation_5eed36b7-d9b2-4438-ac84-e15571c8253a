'use client';
import { useState } from 'react';
import { License } from '@/hooks/license/useLicenseManagement';
import { useLicenseManagement } from '@/hooks/license/useLicenseManagement';
import { Key, Copy, Eye, EyeOff, RotateCcw, Trash2, CheckCircle, AlertCircle, Shield } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface ApiKeyManagementProps {
  licenses: License[];
  onRefetch: () => void;
}

export default function ApiKeyManagement({ licenses, onRefetch }: ApiKeyManagementProps) {
  const { revokeApiKey, updateApiKey } = useLicenseManagement();
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  const activeLicenses = licenses.filter(license => license.isActive);
  const allApiKeys = activeLicenses.flatMap(license => 
    license.apiKeys.map(key => ({ ...key, licenseType: license.licenseType, spigotUsername: license.spigotUsername }))
  );

  const toggleKeyVisibility = (keyId: string) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  const handleToggleKeyStatus = async (keyId: string, currentStatus: boolean) => {
    setLoading(true);
    try {
      const success = await updateApiKey(keyId, { isActive: !currentStatus });
      if (success) {
        toast.success(`API key ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        onRefetch();
      }
    } catch (error) {
      toast.error('Failed to update API key status');
    }
    setLoading(false);
  };

  const handleRevokeKey = async (keyId: string, serverName?: string) => {
    if (!confirm(`Are you sure you want to permanently revoke the API key for ${serverName || 'this server'}? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    try {
      const success = await revokeApiKey(keyId);
      if (success) {
        toast.success('API key revoked successfully');
        onRefetch();
      }
    } catch (error) {
      toast.error('Failed to revoke API key');
    }
    setLoading(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const maskApiKey = (keyHash: string) => {
    // Show first 8 and last 4 characters of the hash
    if (keyHash.length <= 12) return keyHash;
    return `${keyHash.substring(0, 8)}${'*'.repeat(Math.min(keyHash.length - 12, 20))}${keyHash.substring(keyHash.length - 4)}`;
  };

  if (allApiKeys.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-8 text-center">
        <Key className="mx-auto text-gray-500 mb-4" size={48} />
        <h3 className="text-gray-400 font-semibold text-lg mb-2">No API Keys</h3>
        <p className="text-gray-500 mb-4">You haven't generated any API keys yet.</p>
        <p className="text-gray-400 text-sm">
          Go to the Server Management tab to add servers and generate API keys.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* API Key Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <Key className="text-blue-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total API Keys</p>
              <p className="text-2xl font-bold text-white">{allApiKeys.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <CheckCircle className="text-green-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Active Keys</p>
              <p className="text-2xl font-bold text-white">
                {allApiKeys.filter(key => key.isActive).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <Shield className="text-purple-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Recently Used</p>
              <p className="text-2xl font-bold text-white">
                {allApiKeys.filter(key => key.lastUsed && 
                  new Date(key.lastUsed) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                ).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* API Keys List */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">API Key Management</h3>
          <p className="text-gray-400 text-sm mt-1">
            Manage your server API keys and monitor their usage
          </p>
        </div>

        <div className="divide-y divide-gray-700">
          {allApiKeys.map((apiKey) => (
            <div key={apiKey.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-3 h-3 rounded-full ${apiKey.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                    <h4 className="text-white font-medium">
                      {apiKey.serverName || `Server ${apiKey.serverIp}`}
                    </h4>
                    <span className="px-2 py-1 bg-blue-900/20 text-blue-400 text-xs rounded-full border border-blue-600">
                      {apiKey.licenseType}
                    </span>
                    {!apiKey.isActive && (
                      <span className="px-2 py-1 bg-red-900/20 text-red-400 text-xs rounded-full border border-red-600">
                        Inactive
                      </span>
                    )}
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Server IP:</span>
                      <div className="flex items-center gap-2 mt-1">
                        <code className="text-white bg-gray-700 px-2 py-1 rounded text-xs">
                          {apiKey.serverIp}
                        </code>
                        <button
                          onClick={() => copyToClipboard(apiKey.serverIp, 'Server IP')}
                          className="text-gray-400 hover:text-white transition-colors"
                          title="Copy IP"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                    </div>

                    <div>
                      <span className="text-gray-400">API Key Hash:</span>
                      <div className="flex items-center gap-2 mt-1">
                        <code className="text-white bg-gray-700 px-2 py-1 rounded text-xs font-mono">
                          {visibleKeys.has(apiKey.id) ? apiKey.keyHash : maskApiKey(apiKey.keyHash)}
                        </code>
                        <button
                          onClick={() => toggleKeyVisibility(apiKey.id)}
                          className="text-gray-400 hover:text-white transition-colors"
                          title={visibleKeys.has(apiKey.id) ? 'Hide hash' : 'Show hash'}
                        >
                          {visibleKeys.has(apiKey.id) ? <EyeOff size={14} /> : <Eye size={14} />}
                        </button>
                        <button
                          onClick={() => copyToClipboard(apiKey.keyHash, 'API Key Hash')}
                          className="text-gray-400 hover:text-white transition-colors"
                          title="Copy API key hash"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        ⚠️ Original API key was shown only once during creation
                      </p>
                    </div>

                    <div>
                      <span className="text-gray-400">Created:</span>
                      <span className="text-white ml-2">{formatDate(apiKey.createdAt)}</span>
                    </div>

                    <div>
                      <span className="text-gray-400">Last Used:</span>
                      <span className="text-white ml-2">
                        {apiKey.lastUsed ? formatDate(apiKey.lastUsed) : 'Never'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  <button
                    onClick={() => handleToggleKeyStatus(apiKey.id, apiKey.isActive)}
                    disabled={loading}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      apiKey.isActive
                        ? 'bg-yellow-900/20 text-yellow-400 border border-yellow-600 hover:bg-yellow-900/30'
                        : 'bg-green-900/20 text-green-400 border border-green-600 hover:bg-green-900/30'
                    }`}
                    title={apiKey.isActive ? 'Deactivate key' : 'Activate key'}
                  >
                    {apiKey.isActive ? 'Deactivate' : 'Activate'}
                  </button>

                  <button
                    onClick={() => handleRevokeKey(apiKey.id, apiKey.serverName)}
                    disabled={loading}
                    className="px-3 py-1 bg-red-900/20 text-red-400 border border-red-600 rounded text-sm font-medium hover:bg-red-900/30 transition-colors"
                    title="Permanently revoke key"
                  >
                    Revoke
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-6">
        <div className="flex items-start gap-3">
          <Key className="text-blue-500 mt-1" size={20} />
          <div>
            <h3 className="text-blue-400 font-semibold mb-2">How to Use API Keys</h3>
            <div className="text-gray-300 text-sm space-y-2">
              <p>
                <strong>1. Plugin Configuration:</strong> Copy your API key and add it to your plugin's config file.
              </p>
              <p>
                <strong>2. IP Restriction:</strong> Each key only works from its registered server IP address.
              </p>
              <p>
                <strong>3. Security:</strong> Never share your API keys. If compromised, revoke them immediately.
              </p>
              <p>
                <strong>4. Monitoring:</strong> Check the "Last Used" timestamp to monitor key activity.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
