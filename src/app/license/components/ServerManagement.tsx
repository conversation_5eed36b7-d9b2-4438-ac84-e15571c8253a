'use client';
import { useState } from 'react';
import { License } from '@/hooks/license/useLicenseManagement';
import { useLicenseManagement } from '@/hooks/license/useLicenseManagement';
import { Server, Plus, Trash2, Edit2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface ServerManagementProps {
  licenses: License[];
  onRefetch: () => void;
}

export default function ServerManagement({ licenses, onRefetch }: ServerManagementProps) {
  const { createApiKey, revokeApiKey, updateApiKey } = useLicenseManagement();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    licenseId: '',
    serverIp: '',
    serverName: ''
  });
  const [editFormData, setEditFormData] = useState({
    serverName: ''
  });
  const [loading, setLoading] = useState(false);

  const activeLicenses = licenses.filter(license => license.isActive);

  const handleAddServer = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.licenseId || !formData.serverIp) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Validate IP address format
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!ipRegex.test(formData.serverIp)) {
      toast.error('Please enter a valid IP address');
      return;
    }

    setLoading(true);
    try {
      const result = await createApiKey({
        licenseId: formData.licenseId,
        serverIp: formData.serverIp,
        serverName: formData.serverName || undefined
      });

      if (result) {
        toast.success('Server added successfully!');
        setShowAddForm(false);
        setFormData({ licenseId: '', serverIp: '', serverName: '' });
        onRefetch();
        
        // Show the API key to the user (only shown once)
        toast.success(
          `API Key: ${result.apiKey}\n\nSave this key securely - it won't be shown again!`,
          { duration: 10000 }
        );
      }
    } catch (error) {
      toast.error('Failed to add server');
    }
    setLoading(false);
  };

  const handleRemoveServer = async (keyId: string, serverName?: string) => {
    if (!confirm(`Are you sure you want to remove ${serverName || 'this server'}? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    try {
      const success = await revokeApiKey(keyId);
      if (success) {
        toast.success('Server removed successfully');
        onRefetch();
      }
    } catch (error) {
      toast.error('Failed to remove server');
    }
    setLoading(false);
  };

  const handleEditServer = async (keyId: string) => {
    setLoading(true);
    try {
      const success = await updateApiKey(keyId, {
        serverName: editFormData.serverName || undefined
      });
      if (success) {
        toast.success('Server updated successfully');
        setEditingKey(null);
        setEditFormData({ serverName: '' });
        onRefetch();
      }
    } catch (error) {
      toast.error('Failed to update server');
    }
    setLoading(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (activeLicenses.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-8 text-center">
        <Server className="mx-auto text-gray-500 mb-4" size={48} />
        <h3 className="text-gray-400 font-semibold text-lg mb-2">No Active Licenses</h3>
        <p className="text-gray-500">You need an active license to manage servers.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Add Server Form */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Add New Server</h3>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Add Server
          </button>
        </div>

        {showAddForm && (
          <form onSubmit={handleAddServer} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                License <span className="text-red-400">*</span>
              </label>
              <select
                value={formData.licenseId}
                onChange={(e) => setFormData({ ...formData, licenseId: e.target.value })}
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                required
              >
                <option value="">Select a license</option>
                {activeLicenses.map((license) => (
                  <option key={license.id} value={license.id}>
                    {license.licenseType.charAt(0).toUpperCase() + license.licenseType.slice(1)} License
                    {license.spigotUsername && ` (${license.spigotUsername})`}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Server IP Address <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={formData.serverIp}
                onChange={(e) => setFormData({ ...formData, serverIp: e.target.value })}
                placeholder="*************"
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                required
              />
              <p className="text-gray-400 text-xs mt-1">
                Enter the public IP address of your Minecraft server
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Server Name (Optional)
              </label>
              <input
                type="text"
                value={formData.serverName}
                onChange={(e) => setFormData({ ...formData, serverName: e.target.value })}
                placeholder="My Minecraft Server"
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              />
              <p className="text-gray-400 text-xs mt-1">
                A friendly name to help you identify this server
              </p>
            </div>

            <div className="flex gap-3">
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {loading ? 'Adding...' : 'Add Server'}
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Server List */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Registered Servers</h3>
        </div>

        <div className="divide-y divide-gray-700">
          {activeLicenses.map((license) => (
            <div key={license.id}>
              {license.apiKeys.length > 0 && (
                <div className="p-6">
                  <h4 className="text-white font-medium mb-4">
                    {license.licenseType.charAt(0).toUpperCase() + license.licenseType.slice(1)} License
                    {license.spigotUsername && ` (${license.spigotUsername})`}
                  </h4>
                  
                  <div className="space-y-3">
                    {license.apiKeys.map((apiKey) => (
                      <div key={apiKey.id} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${apiKey.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                          <div>
                            {editingKey === apiKey.id ? (
                              <input
                                type="text"
                                value={editFormData.serverName}
                                onChange={(e) => setEditFormData({ serverName: e.target.value })}
                                placeholder={apiKey.serverName || 'Server name'}
                                className="bg-gray-600 border border-gray-500 rounded px-2 py-1 text-white text-sm"
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') handleEditServer(apiKey.id);
                                  if (e.key === 'Escape') setEditingKey(null);
                                }}
                              />
                            ) : (
                              <div>
                                <p className="text-white font-medium">
                                  {apiKey.serverName || `Server ${apiKey.serverIp}`}
                                </p>
                                <p className="text-gray-400 text-sm">{apiKey.serverIp}</p>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className="text-right text-sm">
                            <p className="text-gray-400">
                              {apiKey.lastUsed ? `Last used ${formatDate(apiKey.lastUsed)}` : 'Never used'}
                            </p>
                            <p className="text-gray-500">
                              Created {formatDate(apiKey.createdAt)}
                            </p>
                          </div>
                          
                          <div className="flex gap-1">
                            {editingKey === apiKey.id ? (
                              <>
                                <button
                                  onClick={() => handleEditServer(apiKey.id)}
                                  className="p-2 text-green-400 hover:bg-gray-600 rounded transition-colors"
                                  title="Save"
                                >
                                  <CheckCircle size={16} />
                                </button>
                                <button
                                  onClick={() => setEditingKey(null)}
                                  className="p-2 text-gray-400 hover:bg-gray-600 rounded transition-colors"
                                  title="Cancel"
                                >
                                  <AlertCircle size={16} />
                                </button>
                              </>
                            ) : (
                              <>
                                <button
                                  onClick={() => {
                                    setEditingKey(apiKey.id);
                                    setEditFormData({ serverName: apiKey.serverName || '' });
                                  }}
                                  className="p-2 text-blue-400 hover:bg-gray-600 rounded transition-colors"
                                  title="Edit server name"
                                >
                                  <Edit2 size={16} />
                                </button>
                                <button
                                  onClick={() => handleRemoveServer(apiKey.id, apiKey.serverName)}
                                  className="p-2 text-red-400 hover:bg-gray-600 rounded transition-colors"
                                  title="Remove server"
                                >
                                  <Trash2 size={16} />
                                </button>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {activeLicenses.every(license => license.apiKeys.length === 0) && (
          <div className="p-8 text-center">
            <Server className="mx-auto text-gray-500 mb-4" size={48} />
            <h3 className="text-gray-400 font-semibold text-lg mb-2">No Servers Registered</h3>
            <p className="text-gray-500">Add your first server to get started with API key authentication.</p>
          </div>
        )}
      </div>
    </div>
  );
}
