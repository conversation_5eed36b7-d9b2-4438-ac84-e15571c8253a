'use client';
import { License } from '@/hooks/license/useLicenseManagement';
import { Shield, Calendar, Server, Key, CheckCircle, AlertCircle } from 'lucide-react';

interface LicenseOverviewProps {
  licenses: License[];
  onRefetch: () => void;
}

export default function LicenseOverview({ licenses, onRefetch }: LicenseOverviewProps) {
  const activeLicenses = licenses.filter(license => license.isActive);
  const totalApiKeys = licenses.reduce((sum, license) => sum + license.apiKeys.length, 0);
  const activeApiKeys = licenses.reduce((sum, license) => 
    sum + license.apiKeys.filter(key => key.isActive).length, 0
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getLicenseStatusBadge = (license: License) => {
    if (!license.isActive) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-600">
          <AlertCircle size={12} className="mr-1" />
          Inactive
        </span>
      );
    }

    if (license.expiresAt) {
      const expiryDate = new Date(license.expiresAt);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilExpiry <= 0) {
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-600">
            <AlertCircle size={12} className="mr-1" />
            Expired
          </span>
        );
      } else if (daysUntilExpiry <= 7) {
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-600">
            <AlertCircle size={12} className="mr-1" />
            Expires in {daysUntilExpiry} days
          </span>
        );
      }
    }

    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/20 text-green-400 border border-green-600">
        <CheckCircle size={12} className="mr-1" />
        Active
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <Shield className="text-blue-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Active Licenses</p>
              <p className="text-2xl font-bold text-white">{activeLicenses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <Server className="text-green-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Servers</p>
              <p className="text-2xl font-bold text-white">{totalApiKeys}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <Key className="text-purple-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Active API Keys</p>
              <p className="text-2xl font-bold text-white">{activeApiKeys}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center">
            <Calendar className="text-orange-500" size={24} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">License Type</p>
              <p className="text-2xl font-bold text-white">Premium</p>
            </div>
          </div>
        </div>
      </div>

      {/* License Details */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">License Details</h3>
        </div>
        
        <div className="divide-y divide-gray-700">
          {licenses.map((license) => (
            <div key={license.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="text-white font-medium">
                      {license.licenseType.charAt(0).toUpperCase() + license.licenseType.slice(1)} License
                    </h4>
                    {getLicenseStatusBadge(license)}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Spigot Username:</span>
                      <span className="text-white ml-2">{license.spigotUsername || 'Not specified'}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Created:</span>
                      <span className="text-white ml-2">{formatDate(license.createdAt)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">API Keys:</span>
                      <span className="text-white ml-2">{license.apiKeys.length}</span>
                    </div>
                    {license.expiresAt && (
                      <div>
                        <span className="text-gray-400">Expires:</span>
                        <span className="text-white ml-2">{formatDate(license.expiresAt)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Recent API Key Activity */}
              {license.apiKeys.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <h5 className="text-sm font-medium text-gray-300 mb-2">Recent Server Activity</h5>
                  <div className="space-y-2">
                    {license.apiKeys.slice(0, 3).map((apiKey) => (
                      <div key={apiKey.id} className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${apiKey.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                          <span className="text-gray-300">
                            {apiKey.serverName || `Server ${apiKey.serverIp}`}
                          </span>
                          <span className="text-gray-500">({apiKey.serverIp})</span>
                        </div>
                        <span className="text-gray-400">
                          {apiKey.lastUsed ? `Last used ${formatDate(apiKey.lastUsed)}` : 'Never used'}
                        </span>
                      </div>
                    ))}
                    {license.apiKeys.length > 3 && (
                      <p className="text-xs text-gray-500">
                        +{license.apiKeys.length - 3} more servers
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-900/20 border border-blue-600 rounded-lg p-6">
        <div className="flex items-start gap-3">
          <Shield className="text-blue-500 mt-1" size={20} />
          <div>
            <h3 className="text-blue-400 font-semibold mb-2">Enhanced Security</h3>
            <p className="text-gray-300 text-sm mb-2">
              Your licenses are now protected with IP-restricted API keys. Each API key is bound to a specific server IP address, 
              ensuring that even if a key is compromised, it can only be used from your registered servers.
            </p>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• API keys are tied to specific server IP addresses</li>
              <li>• Keys cannot be used from unauthorized locations</li>
              <li>• You can revoke access instantly if needed</li>
              <li>• Monitor usage and last access times</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
