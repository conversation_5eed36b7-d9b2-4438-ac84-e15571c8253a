import { NextRequest, NextResponse } from 'next/server';
import {
  validateLicenseApiKey,
  getClientIp,
  checkRateLimit,
  securityHeaders,
  logSecurityEvent
} from '@/lib/license-security';

export async function POST(req: NextRequest) {
  try {
    const { apiKey, licenseType = 'headsteal' } = await req.json();

    // Get client IP
    const clientIp = getClientIp(req);

    // Rate limiting
    if (!checkRateLimit(clientIp, 30, 60000)) { // 30 requests per minute
      logSecurityEvent('RATE_LIMIT_EXCEEDED', { clientIp, endpoint: '/api/license/validate' });
      return NextResponse.json(
        { error: 'Rate limit exceeded', valid: false },
        { status: 429, headers: securityHeaders }
      );
    }

    if (!apiKey) {
      logSecurityEvent('MISSING_API_KEY', { clientIp });
      return NextResponse.json(
        { error: 'API key is required', valid: false },
        { status: 400, headers: securityHeaders }
      );
    }

    // Validate the license
    const validation = await validateLicenseApiKey(apiKey, clientIp, licenseType);

    if (!validation.valid) {
      logSecurityEvent('INVALID_LICENSE_ATTEMPT', {
        clientIp,
        error: validation.error,
        licenseType
      });

      return NextResponse.json(
        {
          error: validation.error,
          valid: false,
          details: {
            clientIp,
            reason: validation.error
          }
        },
        { status: 403, headers: securityHeaders }
      );
    }

    // Log successful validation
    logSecurityEvent('LICENSE_VALIDATED', {
      clientIp,
      userId: validation.license?.userId,
      licenseType
    });

    // Return validation success with license info
    return NextResponse.json(
      {
        valid: true,
        license: validation.license
      },
      { headers: securityHeaders }
    );
  } catch (error) {
    console.error('License validation error:', error);
    logSecurityEvent('VALIDATION_ERROR', { error: error.toString() });
    return NextResponse.json(
      { error: 'Internal server error', valid: false },
      { status: 500, headers: securityHeaders }
    );
  }
}

// GET method for backward compatibility (deprecated)
export async function GET(req: NextRequest) {
  return NextResponse.json(
    { 
      error: 'GET method deprecated. Use POST with API key authentication.',
      valid: false,
      migration: {
        message: 'Please update your plugin to use the new API key authentication system.',
        endpoint: '/api/license/validate',
        method: 'POST',
        body: { apiKey: 'your-api-key', licenseType: 'headsteal' }
      }
    },
    { status: 410 } // Gone
  );
}
