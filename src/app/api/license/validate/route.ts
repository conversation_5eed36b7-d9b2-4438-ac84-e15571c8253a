import { NextRequest, NextResponse } from 'next/server';
import {
  validateLicenseApiKey,
  getClientIp,
  checkRateLimit,
  securityHeaders
} from '@/lib/license-security';
import { logSecurityEvent, formatErrorResponse } from '@/lib/license-errors';

export async function POST(req: NextRequest) {
  try {
    const { apiKey, licenseType = 'headsteal' } = await req.json();

    // Get client IP
    const clientIp = getClientIp(req);
    const userAgent = req.headers.get('user-agent') || undefined;

    // Rate limiting
    const rateLimit = checkRateLimit(clientIp, 30, 60000); // 30 requests per minute
    if (!rateLimit.allowed) {
      logSecurityEvent('RATE_LIMIT', {
        clientIp,
        userAgent,
        details: { endpoint: '/api/license/validate' }
      });
      return NextResponse.json(
        { error: 'Rate limit exceeded', valid: false },
        { status: 429, headers: securityHeaders }
      );
    }

    if (!apiKey) {
      logSecurityEvent('AUTH_FAILURE', {
        clientIp,
        userAgent,
        error: 'Missing API key'
      });
      return NextResponse.json(
        { error: 'API key is required', valid: false },
        { status: 400, headers: securityHeaders }
      );
    }

    // Validate the license
    const validation = await validateLicenseApiKey(apiKey, clientIp, licenseType, userAgent);

    if (!validation.valid) {
      // Error logging is handled inside validateLicenseApiKey
      return NextResponse.json(
        formatErrorResponse(validation.error),
        { status: validation.error.statusCode, headers: securityHeaders }
      );
    }

    // Return validation success with license info
    return NextResponse.json(
      {
        valid: true,
        license: validation.license
      },
      { headers: securityHeaders }
    );
  } catch (error) {
    console.error('License validation error:', error);
    logSecurityEvent('ERROR', {
      clientIp: getClientIp(req),
      userAgent: req.headers.get('user-agent') || undefined,
      error: 'Validation endpoint error',
      details: { originalError: error instanceof Error ? error.message : String(error) }
    });
    return NextResponse.json(
      { error: 'Internal server error', valid: false },
      { status: 500, headers: securityHeaders }
    );
  }
}

// GET method for backward compatibility (deprecated)
export async function GET(_req: NextRequest) {
  return NextResponse.json(
    { 
      error: 'GET method deprecated. Use POST with API key authentication.',
      valid: false,
      migration: {
        message: 'Please update your plugin to use the new API key authentication system.',
        endpoint: '/api/license/validate',
        method: 'POST',
        body: { apiKey: 'your-api-key', licenseType: 'headsteal' }
      }
    },
    { status: 410 } // Gone
  );
}
