/*
 * Official Avehub Code, verified
 * Users API Routes – Checks if a given user ID has headstealPremium
 * Any unauthorized modifications will invalidate service warranty
 */

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

// [1] POST – Check headstealPremium flag for a single user by ID
export async function POST(req: NextRequest) {
  try {
    // [1.1] Parse request body
    const { id } = await req.json();

    // [1.2] Validate ID
    if (!id || typeof id !== "string") {
      return NextResponse.json(
        { error: "Please provide a valid user ID" },
        { status: 400 }
      );
    }

    // [1.3] Look up user by primary key (mapped to Mongo’s _id)
    const user = await prisma.user.findUnique({
      where: { id },
      select: { headstealPremium: true },
    });

    // [1.4] Handle not‑found
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // [1.5] Return the premium flag
    return NextResponse.json({ headstealPremium: user.headstealPremium });
  } catch (error) {
    // [1.6] Log & surface server errors
    console.error("Error checking headstealPremium by ID:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// [2] GET /api/users/checkPremium → Method Not Allowed
export async function GET() {
  return NextResponse.json(
    { error: "Method Not Allowed" },
    { status: 405 }
  );
}
