import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';
import crypto from 'crypto';

// Generate a secure API key
function generateApiKey(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Hash an API key for storage
function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { licenseId, serverIp, serverName } = await req.json();

    if (!licenseId || !serverIp) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate IP address format
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!ipRegex.test(serverIp)) {
      return NextResponse.json({ error: 'Invalid IP address format' }, { status: 400 });
    }

    // Verify the license belongs to the user and is active
    const license = await prisma.license.findFirst({
      where: {
        id: licenseId,
        userId: session.user.id,
        isActive: true
      }
    });

    if (!license) {
      return NextResponse.json({ error: 'License not found or inactive' }, { status: 404 });
    }

    // Check if this IP already has an API key for this license
    const existingKey = await prisma.licenseApiKey.findFirst({
      where: {
        licenseId,
        serverIp
      }
    });

    if (existingKey) {
      return NextResponse.json({ 
        error: 'An API key already exists for this server IP' 
      }, { status: 409 });
    }

    // Generate new API key
    const apiKey = generateApiKey();
    const keyHash = hashApiKey(apiKey);

    // Create the API key record
    const newApiKey = await prisma.licenseApiKey.create({
      data: {
        licenseId,
        keyHash,
        serverIp,
        serverName: serverName || null
      }
    });

    // Return the API key (only time it's shown in plain text)
    return NextResponse.json({
      apiKey, // Plain text API key (only returned once)
      keyData: newApiKey
    });
  } catch (err) {
    console.error('API key creation error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
