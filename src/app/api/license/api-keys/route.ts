import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';
import crypto from 'crypto';
import {
  LicenseErrorCode,
  createLicenseError,
  formatErrorResponse,
  isValidIPAddress
} from '@/lib/license-errors';

// Generate a secure API key
function generateApiKey(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Hash an API key for storage
function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      const error = createLicenseError(LicenseErrorCode.UNAUTHORIZED);
      return NextResponse.json(formatErrorResponse(error), { status: error.statusCode });
    }

    const { licenseId, serverIp, serverName } = await req.json();

    if (!licenseId || !serverIp) {
      const error = createLicenseError(LicenseErrorCode.VALIDATION_ERROR, {
        missing: !licenseId ? 'licenseId' : 'serverIp'
      });
      return NextResponse.json(formatErrorResponse(error), { status: error.statusCode });
    }

    // Validate IP address format
    if (!isValidIPAddress(serverIp)) {
      const error = createLicenseError(LicenseErrorCode.INVALID_IP_FORMAT, { serverIp });
      return NextResponse.json(formatErrorResponse(error), { status: error.statusCode });
    }

    // Verify the license belongs to the user and is active
    const license = await prisma.license.findFirst({
      where: {
        id: licenseId,
        userId: session.user.id,
        isActive: true
      }
    });

    if (!license) {
      const error = createLicenseError(LicenseErrorCode.LICENSE_NOT_FOUND, { licenseId });
      return NextResponse.json(formatErrorResponse(error), { status: error.statusCode });
    }

    // Check if this IP already has an API key for this license
    const existingKey = await prisma.licenseApiKey.findFirst({
      where: {
        licenseId,
        serverIp
      }
    });

    if (existingKey) {
      const error = createLicenseError(LicenseErrorCode.API_KEY_EXISTS, { serverIp, licenseId });
      return NextResponse.json(formatErrorResponse(error), { status: error.statusCode });
    }

    // Generate new API key
    const apiKey = generateApiKey();
    const keyHash = hashApiKey(apiKey);

    // Create the API key record
    const newApiKey = await prisma.licenseApiKey.create({
      data: {
        licenseId,
        keyHash,
        serverIp,
        serverName: serverName || null
      }
    });

    // Return the API key (only time it's shown in plain text)
    return NextResponse.json({
      apiKey, // Plain text API key (only returned once)
      keyData: newApiKey
    });
  } catch (err) {
    console.error('API key creation error', err);
    const error = createLicenseError(LicenseErrorCode.INTERNAL_ERROR, {
      originalError: err instanceof Error ? err.message : String(err)
    });
    return NextResponse.json(formatErrorResponse(error), { status: error.statusCode });
  }
}
