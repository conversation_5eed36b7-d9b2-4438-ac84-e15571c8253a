import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';

export async function PATCH(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(req.url);
    const keyId = url.pathname.split('/').pop() as string;
    const updates = await req.json();

    // Verify the API key belongs to the user
    const apiKey = await prisma.licenseApiKey.findFirst({
      where: {
        id: keyId,
        license: {
          userId: session.user.id
        }
      }
    });

    if (!apiKey) {
      return NextResponse.json({ error: 'API key not found' }, { status: 404 });
    }

    // Update the API key
    const updatedKey = await prisma.licenseApiKey.update({
      where: { id: keyId },
      data: {
        ...(updates.serverName !== undefined && { serverName: updates.serverName }),
        ...(updates.isActive !== undefined && { isActive: updates.isActive }),
        updatedAt: new Date()
      }
    });

    return NextResponse.json({ apiKey: updatedKey });
  } catch (err) {
    console.error('API key update error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(req.url);
    const keyId = url.pathname.split('/').pop() as string;

    // Verify the API key belongs to the user
    const apiKey = await prisma.licenseApiKey.findFirst({
      where: {
        id: keyId,
        license: {
          userId: session.user.id
        }
      }
    });

    if (!apiKey) {
      return NextResponse.json({ error: 'API key not found' }, { status: 404 });
    }

    // Delete the API key
    await prisma.licenseApiKey.delete({
      where: { id: keyId }
    });

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('API key deletion error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
