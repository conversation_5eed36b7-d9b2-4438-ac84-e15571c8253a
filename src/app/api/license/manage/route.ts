import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';

export async function GET(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all licenses for the user with their API keys
    const licenses = await prisma.license.findMany({
      where: { userId: session.user.id },
      include: {
        apiKeys: {
          orderBy: { createdAt: 'desc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({ licenses });
  } catch (err) {
    console.error('License management fetch error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
