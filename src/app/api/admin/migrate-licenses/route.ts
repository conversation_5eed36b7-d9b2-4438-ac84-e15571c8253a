import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';

export async function POST(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Find all approved license requests that don't have corresponding License records
    const approvedRequests = await prisma.licenseRequest.findMany({
      where: {
        status: 'approved'
      },
      include: {
        user: true
      }
    });

    let migratedCount = 0;
    let skippedCount = 0;
    const errors: string[] = [];

    for (const request of approvedRequests) {
      try {
        // Check if a License already exists for this user
        const existingLicense = await prisma.license.findFirst({
          where: {
            userId: request.userId,
            licenseType: 'headsteal'
          }
        });

        if (existingLicense) {
          skippedCount++;
          continue;
        }

        // Create License record
        await prisma.license.create({
          data: {
            userId: request.userId,
            spigotUsername: request.spigotUsername,
            licenseType: 'headsteal',
            isActive: true,
            createdAt: request.requestedAt // Use the original request date
          }
        });

        migratedCount++;
      } catch (error) {
        const errorMsg = `Failed to migrate license for user ${request.userId}: ${error}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return NextResponse.json({
      success: true,
      migrated: migratedCount,
      skipped: skippedCount,
      errors: errors.length > 0 ? errors : undefined,
      message: `Migration completed. ${migratedCount} licenses migrated, ${skippedCount} skipped.`
    });
  } catch (err) {
    console.error('License migration error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
