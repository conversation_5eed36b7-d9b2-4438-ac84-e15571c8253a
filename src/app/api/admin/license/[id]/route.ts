// src/app/api/admin/license/[id]/route.ts
import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import prisma from '@/lib/db';

export async function PATCH(req: Request) {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const url = new URL(req.url);
    const id = url.pathname.split('/').pop() as string;
    const { status } = await req.json();

    if (!['approved','rejected'].includes(status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }

    const updated = await prisma.licenseRequest.update({
      where: { id },
      data: { status },
    });

    if (status === 'approved') {
      // grant premium without throwing if user is missing
      const { count } = await prisma.user.updateMany({
        where: { id: updated.userId },
        data: { headstealPremium: true },
      });
      if (count === 0) {
        console.warn(`No user found with id ${updated.userId}; headstealPremium not granted`);
      }
    }

    return NextResponse.json(updated);
  } catch (err) {
    console.error('License update error', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
