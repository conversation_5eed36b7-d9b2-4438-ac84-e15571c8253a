import { NextResponse } from "next/server";
import prisma from "@/lib/db";
import { auth } from "@/auth";
import { getFilePreview } from "@/lib/appwrite";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.admin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // 1️⃣ Fetch all license requests *without* including `user`
    const requests = await prisma.licenseRequest.findMany({
      orderBy: { requestedAt: "desc" },
      select: {
        id: true,
        userId: true,
        status: true,
        spigotUsername: true,
        screenshotUrl: true,
        requestedAt: true,
        updatedAt: true,
      },
    });

    // 2️⃣ Batch‑fetch the users who actually exist
    const userIds = Array.from(new Set(requests.map((r) => r.userId)));
    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, email: true, name: true },
    });

    // 3️⃣ Build a lookup map from userId → { email, name }
    const userMap: Record<string, { email: string; name: string }> = {};
    for (const u of users) {
      userMap[u.id] = { email: u.email!, name: u.name! };
    }

    // 4️⃣ Merge them, allowing missing users (they’ll become null)
    const formatted = requests.map((r) => {
      const u = userMap[r.userId];
      return {
        id: r.id,
        userId: r.userId,
        status: r.status,
        spigotUsername: r.spigotUsername,
        screenshotUrl: getFilePreview(r.screenshotUrl),
        requestedAt: r.requestedAt,
        updatedAt: r.updatedAt,
        userEmail: u?.email ?? null,
        userName: u?.name ?? null,
      };
    });

    return NextResponse.json(formatted);
  } catch (err) {
    console.error("License list error", err);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

