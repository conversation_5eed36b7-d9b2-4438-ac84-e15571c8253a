import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/db";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const currentId = url.pathname.split("/").slice(-2, -1)[0]; // Get the ID from the path

    if (!currentId) {
      return NextResponse.json({ error: "Current announcement ID is required" }, { status: 400 });
    }

    // Get all announcements ordered by creation date (oldest first)
    const announcements = await prisma.announcement.findMany({
      select: { id: true, title: true, createdAt: true },
      orderBy: { createdAt: "asc" },
    });

    // Find the current announcement index
    const currentIndex = announcements.findIndex((announcement: { id: string }) => announcement.id === currentId);
    
    if (currentIndex === -1) {
      return NextResponse.json({ error: "Current announcement not found" }, { status: 404 });
    }

    // Calculate previous and next announcements
    // With asc order: index 0 = oldest, higher index = newer
    // Previous = older announcement (lower index), Next = newer announcement (higher index)
    const previousAnnouncement = currentIndex > 0 ? announcements[currentIndex - 1] : null; // Older
    const nextAnnouncement = currentIndex < announcements.length - 1 ? announcements[currentIndex + 1] : null; // Newer

    const navigationData = {
      current: {
        id: announcements[currentIndex].id,
        title: announcements[currentIndex].title,
        position: currentIndex + 1,
        total: announcements.length
      },
      previous: previousAnnouncement ? {
        id: previousAnnouncement.id,
        title: previousAnnouncement.title
      } : null,
      next: nextAnnouncement ? {
        id: nextAnnouncement.id,
        title: nextAnnouncement.title
      } : null
    };

    return NextResponse.json(navigationData, { status: 200 });
  } catch (error) {
    console.error("Error fetching announcement navigation:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
