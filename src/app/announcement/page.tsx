"use client";
import { useFetchAllAnnouncements } from "@/hooks/announcement/useFetchAllAnnouncements";
import { AnnouncementCard } from "@/components/announcement/AnnouncementCard";
import { AnnouncementFilters } from "@/components/announcement/AnnouncementFilters";
import { Header } from "@/components/Header";
import { motion } from "framer-motion";
import { Inter } from "next/font/google";
import LoadingDots from "@/components/animations/Loading";
import { Megaphone, Search } from "lucide-react";

const inter = Inter({ subsets: ["latin"], weight: ["400", "700"] });

export default function AnnouncementsPage() {
  const { 
    announcements, 
    allAnnouncements, 
    loading, 
    error, 
    filters, 
    updateFilters 
  } = useFetchAllAnnouncements();

  if (loading) return (
    <div className="flex items-center justify-center min-h-screen">
      <LoadingDots />
    </div>
  );

  if (error) return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <Header />
        <div className="mt-16 p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600 dark:text-gray-300">{error}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      <Header />
      <div className={`${inter.className} min-h-screen bg-gray-50 dark:bg-gray-900 p-8`}>
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12 py-16"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <Megaphone className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              <h1 className="text-4xl font-extrabold text-gray-800 dark:text-white">
                Announcements
              </h1>
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Stay updated with the latest news, updates, and important information from our team.
            </p>
          </motion.div>

          {/* Filters */}
          <AnnouncementFilters
            filters={filters}
            onFilterChange={updateFilters}
            totalCount={allAnnouncements.length}
            filteredCount={announcements.length}
          />

          {/* Announcements Grid */}
          {announcements.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center py-16"
            >
              <Search className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                No announcements found
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {allAnnouncements.length === 0 
                  ? "There are no announcements available at the moment."
                  : "Try adjusting your filters to see more results."
                }
              </p>
            </motion.div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {announcements.map((announcement, index) => (
                <AnnouncementCard
                  key={announcement.id}
                  announcement={announcement}
                  index={index}
                />
              ))}
            </div>
          )}

          {/* Load more hint (for future pagination) */}
          {announcements.length > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="text-center mt-12 py-8"
            >
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                Showing {announcements.length} announcement{announcements.length !== 1 ? 's' : ''}
                {filters.dateRange && ' in selected date range'}
              </p>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}
