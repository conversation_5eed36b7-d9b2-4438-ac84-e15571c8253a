"use client";
import { useFetchAnnouncementWithNavigation } from "@/hooks/announcement/useFetchAnnouncementWithNavigation";
import { useKeyboardNavigation } from "@/hooks/announcement/useKeyboardNavigation";
import { usePrefetchAnnouncements } from "@/hooks/announcement/usePrefetchAnnouncements";
import { AnnouncementNavigation } from "@/components/announcement/AnnouncementNavigation";
import { AnnouncementBreadcrumb } from "@/components/announcement/AnnouncementBreadcrumb";
import { PageTransition } from "@/components/animations/PageTransition";
import { Header } from "@/components/Header";
import { motion } from "framer-motion";
import { Inter } from "next/font/google";
import LoadingDots from "@/components/animations/Loading";
import { useParams } from "next/navigation";

const inter = Inter({ subsets: ["latin"], weight: ["400", "700"] });

export default function AnnouncementPage() {
  const params = useParams();
  const id = params.id as string;
  const { announcement, navigation, loading, error } = useFetchAnnouncementWithNavigation(id);
  
  // Enable keyboard navigation
  useKeyboardNavigation(navigation);
  
  // Prefetch adjacent announcements for faster navigation
  usePrefetchAnnouncements(navigation);

  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  };

  if (loading) return (
    <div className="flex items-center justify-center min-h-screen">
      <LoadingDots />
    </div>
  );
  
  if (error) return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <Header />
        <div className="mt-16 p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600 dark:text-gray-300">{error}</p>
          {error === "Announcement not found" && (
            <p className="text-sm text-gray-500 mt-2">The announcement you're looking for doesn't exist or may have been removed.</p>
          )}
        </div>
      </div>
    </div>
  );
  
  if (!announcement) return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <Header />
        <div className="mt-16 p-8">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">No Announcement Found</h1>
          <p className="text-gray-600 dark:text-gray-300">The requested announcement could not be found.</p>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      <Header />
      <PageTransition id={id}>
        <div className={`${inter.className} min-h-screen flex flex-col items-center bg-gray-50 dark:bg-gray-900 p-8 space-y-8`}>
          <main className="w-full max-w-5xl py-16">
            {/* Breadcrumb */}
            <AnnouncementBreadcrumb 
              announcementTitle={announcement.title}
              currentPosition={navigation?.current.position}
              totalCount={navigation?.current.total}
            />
            
            <div className="text-left flex flex-col gap-8 mt-8">
              <motion.h1
                className="text-4xl font-extrabold text-gray-800 dark:text-white"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
              >
                {announcement.title}
              </motion.h1>
              <motion.p
                className="text-lg text-gray-600 dark:text-gray-300"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                transition={{ delay: 0.2 }}
              >
                {announcement.description}
              </motion.p>
              <motion.p
                className="text-gray-500 dark:text-gray-400 italic"
                variants={fadeInUp}
                initial="hidden"
                animate="visible"
                transition={{ delay: 0.4 }}
              >
                {announcement.footer}
              </motion.p>
            </div>

            {/* Navigation Section */}
            {navigation && (
              <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                <AnnouncementNavigation navigation={navigation} />
              </div>
            )}
          </main>
        </div>
      </PageTransition>
    </div>
  );
}
