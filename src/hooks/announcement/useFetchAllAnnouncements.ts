import { useState, useEffect } from "react";

interface Announcement {
  id: string;
  title: string;
  description: string;
  footer: string;
  imageUrl?: string;
  createdAt: string;
}

interface FilterOptions {
  sortBy: 'newest' | 'oldest';
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export function useFetchAllAnnouncements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [filteredAnnouncements, setFilteredAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({ sortBy: 'newest' });

  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/announcements', {
          method: 'GET',
        });

        const text = await response.text();
        if (!text) {
          throw new Error('Empty response from the announcements API');
        }

        const data = JSON.parse(text);
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch announcements');
        }

        setAnnouncements(data);
        setFilteredAnnouncements(data);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchAnnouncements();
  }, []);

  useEffect(() => {
    let filtered = [...announcements];

    // Apply date range filter
    if (filters.dateRange) {
      filtered = filtered.filter(announcement => {
        const announcementDate = new Date(announcement.createdAt);
        return announcementDate >= filters.dateRange!.from && 
               announcementDate <= filters.dateRange!.to;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      
      return filters.sortBy === 'newest' 
        ? dateB.getTime() - dateA.getTime()
        : dateA.getTime() - dateB.getTime();
    });

    setFilteredAnnouncements(filtered);
  }, [announcements, filters]);

  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return {
    announcements: filteredAnnouncements,
    allAnnouncements: announcements,
    loading,
    error,
    filters,
    updateFilters,
  };
}
