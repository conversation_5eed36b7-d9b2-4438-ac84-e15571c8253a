import { useState, useEffect } from "react";

interface Announcement {
  id: string;
  title: string;
  description: string;
  footer: string;
  imageUrl?: string;
}

interface NavigationData {
  current: {
    id: string;
    title: string;
    position: number;
    total: number;
  };
  previous: {
    id: string;
    title: string;
  } | null;
  next: {
    id: string;
    title: string;
  } | null;
}

export function useFetchAnnouncementWithNavigation(id: string) {
  const [announcement, setAnnouncement] = useState<Announcement | null>(null);
  const [navigation, setNavigation] = useState<NavigationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!id) {
        setError("No announcement ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        // Fetch announcement and navigation data in parallel
        const [announcementResponse, navigationResponse] = await Promise.all([
          fetch(`/api/announcements/${id}`),
          fetch(`/api/announcements/${id}/navigation`)
        ]);

        // Handle announcement response
        const announcementText = await announcementResponse.text();
        if (!announcementText) {
          throw new Error("Empty response from the announcements API");
        }

        const announcementData = JSON.parse(announcementText);
        if (!announcementResponse.ok) {
          if (announcementResponse.status === 404) {
            throw new Error("Announcement not found");
          }
          throw new Error(announcementData.error || "Failed to fetch announcement");
        }

        // Handle navigation response
        const navigationText = await navigationResponse.text();
        let navigationData = null;
        
        if (navigationResponse.ok && navigationText) {
          try {
            navigationData = JSON.parse(navigationText);
          } catch (navError) {
            console.warn("Failed to parse navigation data:", navError);
            // Navigation is optional, don't fail the whole request
          }
        }

        setAnnouncement(announcementData);
        setNavigation(navigationData);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred");
        }
        setAnnouncement(null);
        setNavigation(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  return {
    announcement,
    navigation,
    loading,
    error,
  };
}
