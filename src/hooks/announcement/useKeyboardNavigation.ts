import { useEffect } from "react";
import { useRouter } from "next/navigation";

interface NavigationData {
  previous: { id: string } | null;
  next: { id: string } | null;
}

export function useKeyboardNavigation(navigation: NavigationData | null) {
  const router = useRouter();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle navigation if no input elements are focused
      const activeElement = document.activeElement;
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT' ||
        (activeElement as HTMLElement).contentEditable === 'true'
      );

      if (isInputFocused || !navigation) return;

      switch (event.key) {
        case 'ArrowLeft':
        case 'h':
          event.preventDefault();
          if (navigation.previous) {
            router.push(`/announcement/${navigation.previous.id}`); // Go to older
          }
          break;
        case 'ArrowRight':
        case 'l':
          event.preventDefault();
          if (navigation.next) {
            router.push(`/announcement/${navigation.next.id}`); // Go to newer
          }
          break;
        case 'Escape':
          event.preventDefault();
          router.push('/announcement');
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [navigation, router]);
}
