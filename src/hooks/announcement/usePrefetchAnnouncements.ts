import { useEffect } from "react";

interface NavigationData {
  previous: { id: string } | null;
  next: { id: string } | null;
}

export function usePrefetchAnnouncements(navigation: NavigationData | null) {
  useEffect(() => {
    if (!navigation) return;

    const prefetchAnnouncement = (id: string) => {
      // Create a link element for prefetching
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = `/api/announcements/${id}`;
      document.head.appendChild(link);

      // Also prefetch the navigation data
      const navLink = document.createElement('link');
      navLink.rel = 'prefetch';
      navLink.href = `/api/announcements/${id}/navigation`;
      document.head.appendChild(navLink);

      // Clean up function to remove links after prefetching
      return () => {
        document.head.removeChild(link);
        document.head.removeChild(navLink);
      };
    };

    const cleanupFunctions: (() => void)[] = [];

    // Prefetch previous announcement
    if (navigation.previous) {
      const cleanup = prefetchAnnouncement(navigation.previous.id);
      if (cleanup) cleanupFunctions.push(cleanup);
    }

    // Prefetch next announcement
    if (navigation.next) {
      const cleanup = prefetchAnnouncement(navigation.next.id);
      if (cleanup) cleanupFunctions.push(cleanup);
    }

    // Cleanup on unmount
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [navigation]);
}
