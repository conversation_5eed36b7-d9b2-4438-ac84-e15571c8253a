import { useState, useEffect } from "react";

interface Announcement {
  id: string;
  title: string;
  description: string;
  footer: string;
  imageUrl?: string;
}

export function useFetchAnnouncementById(id: string) {
  const [announcement, setAnnouncement] = useState<Announcement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnnouncement = async () => {
      if (!id) {
        setError("No announcement ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/announcements/${id}`, {
          method: "GET",
        });

        const text = await response.text();
        console.log("Raw response text:", text);

        if (!text) {
          throw new Error("Empty response from the announcements API");
        }

        const data = JSON.parse(text);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("Announcement not found");
          }
          throw new Error(data.error || "Failed to fetch announcement");
        }

        setAnnouncement(data);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred");
        }
        setAnnouncement(null);
      } finally {
        setLoading(false);
      }
    };

    fetchAnnouncement();
  }, [id]);

  return {
    announcement,
    loading,
    error,
  };
}
