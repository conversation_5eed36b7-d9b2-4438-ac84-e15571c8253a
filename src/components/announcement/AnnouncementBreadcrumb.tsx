import { motion } from "framer-motion";
import { ChevronRight, Home } from "lucide-react";
import Link from "next/link";

interface BreadcrumbProps {
  announcementTitle: string;
  currentPosition?: number;
  totalCount?: number;
}

export function AnnouncementBreadcrumb({ announcementTitle, currentPosition, totalCount }: BreadcrumbProps) {
  const truncatedTitle = announcementTitle.length > 50 
    ? `${announcementTitle.substring(0, 50)}...` 
    : announcementTitle;

  return (
    <motion.nav
      className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-8"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Link 
        href="/" 
        className="flex items-center gap-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
      >
        <Home className="w-4 h-4" />
        <span>Home</span>
      </Link>
      
      <ChevronRight className="w-4 h-4" />
      
      <Link 
        href="/announcement" 
        className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
      >
        Announcements
      </Link>
      
      <ChevronRight className="w-4 h-4" />
      
      <span className="text-gray-800 dark:text-gray-200 font-medium">
        {truncatedTitle}
      </span>
      
      {currentPosition && totalCount && (
        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
          ({currentPosition}/{totalCount})
        </span>
      )}
    </motion.nav>
  );
}
