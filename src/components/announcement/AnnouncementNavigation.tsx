import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, List } from "lucide-react";
import { useRouter } from "next/navigation";

interface NavigationData {
  current: {
    id: string;
    title: string;
    position: number;
    total: number;
  };
  previous: {
    id: string;
    title: string;
  } | null;
  next: {
    id: string;
    title: string;
  } | null;
}

interface AnnouncementNavigationProps {
  navigation: NavigationData;
}

export function AnnouncementNavigation({ navigation }: AnnouncementNavigationProps) {
  const router = useRouter();

  const navigateTo = (id: string) => {
    router.push(`/announcement/${id}`);
  };

  const navigateToList = () => {
    router.push('/announcement');
  };

  return (
    <motion.div
      className="flex flex-col gap-6 w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      {/* Position indicator */}
      <div className="text-center">
        <span className="text-sm text-gray-500 dark:text-gray-400">
          Announcement {navigation.current.position} of {navigation.current.total}
        </span>
      </div>

      {/* Navigation buttons */}
      <div className="flex items-center justify-between gap-4">
        {/* Left button (Older announcement) */}
        <motion.button
          onClick={() => navigation.previous && navigateTo(navigation.previous.id)}
          disabled={!navigation.previous}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200
            ${navigation.previous 
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg' 
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
            }
          `}
          whileHover={navigation.previous ? { scale: 1.02 } : {}}
          whileTap={navigation.previous ? { scale: 0.98 } : {}}
          title={navigation.previous ? `Go to older: ${navigation.previous.title}` : 'No older announcement'}
        >
          <ArrowLeft className="w-4 h-4" />
          <span className="hidden sm:inline">Older</span>
        </motion.button>

        {/* All announcements button */}
        <motion.button
          onClick={navigateToList}
          className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <List className="w-4 h-4" />
          <span className="hidden sm:inline">All Announcements</span>
        </motion.button>

        {/* Right button (Newer announcement) */}
        <motion.button
          onClick={() => navigation.next && navigateTo(navigation.next.id)}
          disabled={!navigation.next}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200
            ${navigation.next 
              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg' 
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
            }
          `}
          whileHover={navigation.next ? { scale: 1.02 } : {}}
          whileTap={navigation.next ? { scale: 0.98 } : {}}
          title={navigation.next ? `Go to newer: ${navigation.next.title}` : 'No newer announcement'}
        >
          <span className="hidden sm:inline">Newer</span>
          <ArrowRight className="w-4 h-4" />
        </motion.button>
      </div>

      {/* Navigation preview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        {navigation.previous && (
          <motion.div
            className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border-l-4 border-blue-500 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors"
            onClick={() => navigateTo(navigation.previous!.id)}
            whileHover={{ x: -2 }}
          >
            <p className="text-gray-500 dark:text-gray-400 text-xs mb-1">← Older</p>
            <p className="text-gray-800 dark:text-gray-200 font-medium line-clamp-2">
              {navigation.previous.title}
            </p>
          </motion.div>
        )}

        {navigation.next && (
          <motion.div
            className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border-r-4 border-blue-500 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors md:text-right"
            onClick={() => navigateTo(navigation.next!.id)}
            whileHover={{ x: 2 }}
          >
            <p className="text-gray-500 dark:text-gray-400 text-xs mb-1">Newer →</p>
            <p className="text-gray-800 dark:text-gray-200 font-medium line-clamp-2">
              {navigation.next.title}
            </p>
          </motion.div>
        )}
      </div>

      {/* Keyboard shortcuts hint */}
      <div className="text-center text-xs text-gray-400 dark:text-gray-500 border-t border-gray-200 dark:border-gray-700 pt-4">
        <p>
          Use <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs">←</kbd> / 
          <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs mx-1">H</kbd> for older • 
          <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs">→</kbd> / 
          <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs mx-1">L</kbd> for newer • 
          <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs mx-1">ESC</kbd> for all announcements
        </p>
      </div>
    </motion.div>
  );
}
