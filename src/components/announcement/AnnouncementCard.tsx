import { motion } from "framer-motion";
import { Calendar, ArrowRight } from "lucide-react";
import Link from "next/link";

interface Announcement {
  id: string;
  title: string;
  description: string;
  footer: string;
  imageUrl?: string;
  createdAt: string;
}

interface AnnouncementCardProps {
  announcement: Announcement;
  index: number;
}

export function AnnouncementCard({ announcement, index }: AnnouncementCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const truncateText = (text: string, maxLength: number = 150) => {
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/announcement/${announcement.id}`}>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 group-hover:scale-[1.02]">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(announcement.createdAt)}</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all duration-200" />
          </div>

          {/* Content */}
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
              {announcement.title}
            </h3>
            
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed line-clamp-3">
              {truncateText(announcement.description)}
            </p>

            {announcement.footer && (
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                {truncateText(announcement.footer, 100)}
              </p>
            )}
          </div>

          {/* Read more indicator */}
          <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
            <span className="text-blue-600 dark:text-blue-400 text-sm font-medium group-hover:underline">
              Read full announcement →
            </span>
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
