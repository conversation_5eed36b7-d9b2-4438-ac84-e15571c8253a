import { motion } from "framer-motion";
import { Filter, Calendar, ArrowUpDown } from "lucide-react";
import { useState } from "react";

interface FilterOptions {
  sortBy: 'newest' | 'oldest';
  dateRange?: {
    from: Date;
    to: Date;
  };
}

interface AnnouncementFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
  totalCount: number;
  filteredCount: number;
}

export function AnnouncementFilters({ 
  filters, 
  onFilterChange, 
  totalCount, 
  filteredCount 
}: AnnouncementFiltersProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleSortChange = (sortBy: 'newest' | 'oldest') => {
    onFilterChange({ sortBy });
  };

  const handleDateRangeChange = (from: string, to: string) => {
    if (from && to) {
      onFilterChange({
        dateRange: {
          from: new Date(from),
          to: new Date(to)
        }
      });
    } else {
      onFilterChange({ dateRange: undefined });
    }
  };

  const clearDateFilter = () => {
    onFilterChange({ dateRange: undefined });
    setShowDatePicker(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700"
    >
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        {/* Filter Header */}
        <div className="flex items-center gap-3">
          <Filter className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
            Filter Announcements
          </h2>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            ({filteredCount} of {totalCount})
          </span>
        </div>

        {/* Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Sort Controls */}
          <div className="flex items-center gap-2">
            <ArrowUpDown className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <select
              value={filters.sortBy}
              onChange={(e) => handleSortChange(e.target.value as 'newest' | 'oldest')}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
            </select>
          </div>

          {/* Date Filter Controls */}
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <button
              onClick={() => setShowDatePicker(!showDatePicker)}
              className={`px-3 py-2 border rounded-lg text-sm transition-colors ${
                filters.dateRange
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                  : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              {filters.dateRange ? 'Date Range Active' : 'Filter by Date'}
            </button>
          </div>

          {/* Clear Filters */}
          {filters.dateRange && (
            <button
              onClick={clearDateFilter}
              className="px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
            >
              Clear Date Filter
            </button>
          )}
        </div>
      </div>

      {/* Date Picker */}
      {showDatePicker && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                From Date
              </label>
              <input
                type="date"
                onChange={(e) => {
                  const toDate = filters.dateRange?.to ? filters.dateRange.to.toISOString().split('T')[0] : '';
                  handleDateRangeChange(e.target.value, toDate);
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                To Date
              </label>
              <input
                type="date"
                onChange={(e) => {
                  const fromDate = filters.dateRange?.from ? filters.dateRange.from.toISOString().split('T')[0] : '';
                  handleDateRangeChange(fromDate, e.target.value);
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}
