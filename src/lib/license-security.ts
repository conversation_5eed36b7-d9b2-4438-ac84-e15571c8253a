import { NextRequest } from 'next/server';
import prisma from '@/lib/db';
import crypto from 'crypto';
import {
  LicenseErrorCode,
  createLicenseError,
  isValidIPAddress,
  logSecurityEvent,
  checkRateLimit as checkRateLimitUtil
} from '@/lib/license-errors';

// Hash an API key for comparison
export function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

// Get client IP address from request
export function getClientIp(req: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  const cfConnectingIp = req.headers.get('cf-connecting-ip');

  if (cfConnectingIp) return cfConnectingIp;
  if (realIp) return realIp;
  if (forwarded) return forwarded.split(',')[0].trim();

  // No fallback available for NextRequest
  return 'unknown';
}

// Validate API key and IP address
export async function validateLicenseApiKey(
  apiKey: string,
  clientIp: string,
  licenseType: string = 'headsteal',
  userAgent?: string
): Promise<{
  valid: boolean;
  license?: any;
  error?: any;
}> {
  try {
    // Validate inputs
    if (!apiKey) {
      const error = createLicenseError(LicenseErrorCode.INVALID_API_KEY);
      logSecurityEvent('AUTH_FAILURE', {
        clientIp,
        userAgent,
        error: 'Missing API key'
      });
      return { valid: false, error };
    }

    if (!clientIp || clientIp === 'unknown') {
      const error = createLicenseError(LicenseErrorCode.IP_MISMATCH, { clientIp });
      logSecurityEvent('AUTH_FAILURE', {
        clientIp,
        userAgent,
        error: 'Unable to determine client IP'
      });
      return { valid: false, error };
    }

    // Hash the provided API key
    const keyHash = hashApiKey(apiKey);

    // Find the API key and verify it's active and matches the IP
    const apiKeyRecord = await prisma.licenseApiKey.findFirst({
      where: {
        keyHash,
        serverIp: clientIp,
        isActive: true,
        license: {
          isActive: true,
          licenseType
        }
      },
      include: {
        license: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                headstealPremium: true
              }
            }
          }
        }
      }
    });

    if (!apiKeyRecord) {
      const error = createLicenseError(LicenseErrorCode.INVALID_API_KEY, {
        clientIp,
        keyHash: keyHash.substring(0, 8) + '...'
      });

      logSecurityEvent('AUTH_FAILURE', {
        clientIp,
        userAgent,
        error: 'API key not found or IP mismatch',
        details: { licenseType }
      });

      return { valid: false, error };
    }

    // Check if license is expired
    if (apiKeyRecord.license.expiresAt && new Date() > new Date(apiKeyRecord.license.expiresAt)) {
      const error = createLicenseError(LicenseErrorCode.LICENSE_EXPIRED);
      logSecurityEvent('AUTH_FAILURE', {
        clientIp,
        userAgent,
        userId: apiKeyRecord.license.userId,
        licenseId: apiKeyRecord.license.id,
        error: 'License expired'
      });
      return { valid: false, error };
    }

    // Update last used timestamp
    await prisma.licenseApiKey.update({
      where: { id: apiKeyRecord.id },
      data: { lastUsed: new Date() }
    });

    // Log successful authentication
    logSecurityEvent('AUTH_SUCCESS', {
      clientIp,
      userAgent,
      userId: apiKeyRecord.license.userId,
      licenseId: apiKeyRecord.license.id,
      apiKeyId: apiKeyRecord.id
    });

    return {
      valid: true,
      license: {
        id: apiKeyRecord.license.id,
        type: apiKeyRecord.license.licenseType,
        userId: apiKeyRecord.license.userId,
        userEmail: apiKeyRecord.license.user.email,
        premium: apiKeyRecord.license.user.headstealPremium,
        serverIp: apiKeyRecord.serverIp,
        serverName: apiKeyRecord.serverName,
        expiresAt: apiKeyRecord.license.expiresAt
      }
    };
  } catch (error) {
    const licenseError = createLicenseError(LicenseErrorCode.DATABASE_ERROR, {
      originalError: error instanceof Error ? error.message : String(error)
    });

    logSecurityEvent('ERROR', {
      clientIp,
      userAgent,
      error: 'Database error during validation',
      details: { originalError: error instanceof Error ? error.message : String(error) }
    });

    return { valid: false, error: licenseError };
  }
}

// Rate limiting wrapper
export function checkRateLimit(
  ip: string,
  maxRequests: number = 10,
  windowMs: number = 60000
): { allowed: boolean; remaining: number; resetTime: number } {
  return checkRateLimitUtil(ip, maxRequests, windowMs);
}

// Security headers for license endpoints
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

// Validate server IP format
export function validateServerIp(ip: string): { valid: boolean; error?: any } {
  if (!ip || typeof ip !== 'string') {
    return {
      valid: false,
      error: createLicenseError(LicenseErrorCode.INVALID_IP_FORMAT, { ip })
    };
  }

  if (!isValidIPAddress(ip)) {
    return {
      valid: false,
      error: createLicenseError(LicenseErrorCode.INVALID_IP_FORMAT, { ip })
    };
  }

  return { valid: true };
}
