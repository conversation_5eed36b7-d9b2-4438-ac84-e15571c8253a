import { NextRequest } from 'next/server';
import prisma from '@/lib/db';
import crypto from 'crypto';

// Hash an API key for comparison
export function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

// Get client IP address from request
export function getClientIp(req: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  const cfConnectingIp = req.headers.get('cf-connecting-ip');
  
  if (cfConnectingIp) return cfConnectingIp;
  if (realIp) return realIp;
  if (forwarded) return forwarded.split(',')[0].trim();
  
  // Fallback to connection remote address
  return req.ip || 'unknown';
}

// Validate API key and IP address
export async function validateLicenseApiKey(
  apiKey: string, 
  clientIp: string, 
  licenseType: string = 'headsteal'
): Promise<{
  valid: boolean;
  license?: any;
  error?: string;
}> {
  try {
    if (!apiKey || !clientIp || clientIp === 'unknown') {
      return {
        valid: false,
        error: 'Invalid API key or unable to determine client IP'
      };
    }

    // Hash the provided API key
    const keyHash = hashApiKey(apiKey);

    // Find the API key and verify it's active and matches the IP
    const apiKeyRecord = await prisma.licenseApiKey.findFirst({
      where: {
        keyHash,
        serverIp: clientIp,
        isActive: true,
        license: {
          isActive: true,
          licenseType
        }
      },
      include: {
        license: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                headstealPremium: true
              }
            }
          }
        }
      }
    });

    if (!apiKeyRecord) {
      // Log the failed attempt for security monitoring
      console.warn(`Failed license validation attempt from IP ${clientIp} with key hash ${keyHash.substring(0, 8)}...`);
      
      return {
        valid: false,
        error: 'Invalid API key or IP address mismatch'
      };
    }

    // Update last used timestamp
    await prisma.licenseApiKey.update({
      where: { id: apiKeyRecord.id },
      data: { lastUsed: new Date() }
    });

    return {
      valid: true,
      license: {
        id: apiKeyRecord.license.id,
        type: apiKeyRecord.license.licenseType,
        userId: apiKeyRecord.license.userId,
        userEmail: apiKeyRecord.license.user.email,
        premium: apiKeyRecord.license.user.headstealPremium,
        serverIp: apiKeyRecord.serverIp,
        serverName: apiKeyRecord.serverName,
        expiresAt: apiKeyRecord.license.expiresAt
      }
    };
  } catch (error) {
    console.error('License validation error:', error);
    return {
      valid: false,
      error: 'Internal server error'
    };
  }
}

// Rate limiting for security
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(ip: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now();
  const key = ip;
  
  const current = rateLimitMap.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}

// Security headers for license endpoints
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

// Log security events
export function logSecurityEvent(event: string, details: any) {
  console.log(`[SECURITY] ${event}:`, {
    timestamp: new Date().toISOString(),
    ...details
  });
}
