// License system error types and handling

export enum LicenseErrorCode {
  // Authentication errors
  UNAUTHORIZED = 'UNAUTHORIZED',
  INVALID_API_KEY = 'INVALID_API_KEY',
  IP_MISMATCH = 'IP_MISMATCH',
  RATE_LIMITED = 'RATE_LIMITED',
  
  // License errors
  LICENSE_NOT_FOUND = 'LICENSE_NOT_FOUND',
  LICENSE_INACTIVE = 'LICENSE_INACTIVE',
  LICENSE_EXPIRED = 'LICENSE_EXPIRED',
  
  // API Key errors
  API_KEY_EXISTS = 'API_KEY_EXISTS',
  API_KEY_NOT_FOUND = 'API_KEY_NOT_FOUND',
  INVALID_IP_FORMAT = 'INVALID_IP_FORMAT',
  
  // Server errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

export interface LicenseError {
  code: LicenseErrorCode;
  message: string;
  details?: any;
  statusCode: number;
}

export const LICENSE_ERRORS: Record<LicenseErrorCode, Omit<LicenseError, 'details'>> = {
  [LicenseErrorCode.UNAUTHORIZED]: {
    code: LicenseErrorCode.UNAUTHORIZED,
    message: 'Authentication required',
    statusCode: 401
  },
  [LicenseErrorCode.INVALID_API_KEY]: {
    code: LicenseErrorCode.INVALID_API_KEY,
    message: 'Invalid or inactive API key',
    statusCode: 403
  },
  [LicenseErrorCode.IP_MISMATCH]: {
    code: LicenseErrorCode.IP_MISMATCH,
    message: 'API key not authorized for this IP address',
    statusCode: 403
  },
  [LicenseErrorCode.RATE_LIMITED]: {
    code: LicenseErrorCode.RATE_LIMITED,
    message: 'Too many requests. Please try again later.',
    statusCode: 429
  },
  [LicenseErrorCode.LICENSE_NOT_FOUND]: {
    code: LicenseErrorCode.LICENSE_NOT_FOUND,
    message: 'License not found',
    statusCode: 404
  },
  [LicenseErrorCode.LICENSE_INACTIVE]: {
    code: LicenseErrorCode.LICENSE_INACTIVE,
    message: 'License is inactive or suspended',
    statusCode: 403
  },
  [LicenseErrorCode.LICENSE_EXPIRED]: {
    code: LicenseErrorCode.LICENSE_EXPIRED,
    message: 'License has expired',
    statusCode: 403
  },
  [LicenseErrorCode.API_KEY_EXISTS]: {
    code: LicenseErrorCode.API_KEY_EXISTS,
    message: 'An API key already exists for this server IP',
    statusCode: 409
  },
  [LicenseErrorCode.API_KEY_NOT_FOUND]: {
    code: LicenseErrorCode.API_KEY_NOT_FOUND,
    message: 'API key not found',
    statusCode: 404
  },
  [LicenseErrorCode.INVALID_IP_FORMAT]: {
    code: LicenseErrorCode.INVALID_IP_FORMAT,
    message: 'Invalid IP address format',
    statusCode: 400
  },
  [LicenseErrorCode.DATABASE_ERROR]: {
    code: LicenseErrorCode.DATABASE_ERROR,
    message: 'Database operation failed',
    statusCode: 500
  },
  [LicenseErrorCode.INTERNAL_ERROR]: {
    code: LicenseErrorCode.INTERNAL_ERROR,
    message: 'Internal server error',
    statusCode: 500
  },
  [LicenseErrorCode.VALIDATION_ERROR]: {
    code: LicenseErrorCode.VALIDATION_ERROR,
    message: 'Validation failed',
    statusCode: 400
  }
};

export function createLicenseError(
  code: LicenseErrorCode, 
  details?: any
): LicenseError {
  const baseError = LICENSE_ERRORS[code];
  return {
    ...baseError,
    details
  };
}

export function formatErrorResponse(error: LicenseError) {
  return {
    error: {
      code: error.code,
      message: error.message,
      ...(error.details && { details: error.details })
    },
    valid: false
  };
}

// IP address validation
export function isValidIPAddress(ip: string): boolean {
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

// Security event logging with structured format
export interface SecurityEvent {
  type: 'AUTH_SUCCESS' | 'AUTH_FAILURE' | 'RATE_LIMIT' | 'SUSPICIOUS_ACTIVITY' | 'ERROR';
  timestamp: string;
  clientIp: string;
  userAgent?: string;
  userId?: string;
  licenseId?: string;
  apiKeyId?: string;
  error?: string;
  details?: any;
}

export function logSecurityEvent(
  type: SecurityEvent['type'],
  data: Omit<SecurityEvent, 'type' | 'timestamp'>
) {
  const event: SecurityEvent = {
    type,
    timestamp: new Date().toISOString(),
    ...data
  };

  // Log to console (in production, this would go to a proper logging service)
  console.log(`[SECURITY:${type}]`, JSON.stringify(event, null, 2));

  // In production, you might want to send this to a monitoring service
  // like DataDog, Sentry, or CloudWatch
}

// Rate limiting with memory store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  key: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    const resetTime = now + windowMs;
    rateLimitStore.set(key, { count: 1, resetTime });
    return { allowed: true, remaining: maxRequests - 1, resetTime };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  return { 
    allowed: true, 
    remaining: maxRequests - current.count, 
    resetTime: current.resetTime 
  };
}

// Clean up expired rate limit entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean up every minute
