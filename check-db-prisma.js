/**
 * <PERSON><PERSON><PERSON> to check what data is currently in the database using Prisma
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  console.log('Checking database status with Prisma...');
  
  try {
    // Check each model
    const models = [
      'user',
      'account', 
      'app',
      'appComment',
      'licenseRequest',
      'license',
      'licenseApiKey',
      'plugin',
      'pluginComment',
      'partnershipApplication',
      'staffApplication',
      'announcement'
    ];
    
    console.log('\nDatabase status:');
    console.log('================');
    
    for (const model of models) {
      try {
        const count = await prisma[model].count();
        console.log(`${model}: ${count} records`);
        
        // Show sample data for important models
        if (['user', 'app', 'licenseRequest', 'plugin'].includes(model) && count > 0) {
          const sample = await prisma[model].findFirst();
          console.log(`  Sample:`, JSON.stringify(sample, null, 2));
        }
      } catch (error) {
        console.log(`${model}: ERROR - ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase().catch(console.error);
