const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testLicenseAPI() {
  console.log('Testing License Management API...');
  
  try {
    // Test 1: Check if we can fetch licenses
    console.log('\n1. Testing license fetching...');
    const licenses = await prisma.license.findMany({
      include: {
        apiKeys: true,
        user: {
          select: { email: true, name: true }
        }
      }
    });
    
    console.log(`✅ Found ${licenses.length} licenses`);
    for (const license of licenses) {
      console.log(`   - License ${license.id} (${license.licenseType}) for ${license.user.email}`);
      console.log(`     API Keys: ${license.apiKeys.length}`);
    }
    
    // Test 2: Check if we can fetch API keys
    console.log('\n2. Testing API key fetching...');
    const apiKeys = await prisma.licenseApiKey.findMany({
      include: {
        license: {
          include: {
            user: { select: { email: true } }
          }
        }
      }
    });
    
    console.log(`✅ Found ${apiKeys.length} API keys`);
    for (const key of apiKeys) {
      console.log(`   - Key ${key.id.substring(0, 8)}... for IP ${key.serverIp} (${key.serverName || 'No name'})`);
      console.log(`     Active: ${key.isActive}, License: ${key.license.licenseType}`);
    }
    
    // Test 3: Test API key creation (simulate)
    console.log('\n3. Testing API key creation simulation...');
    const testLicense = licenses[0];
    if (testLicense) {
      console.log(`   Using license: ${testLicense.id}`);
      console.log(`   Would create API key for IP: *************`);
      console.log(`   ✅ API key creation logic should work`);
    }
    
    console.log('\n🎉 All tests passed! License management API should work.');
    
  } catch (error) {
    console.error('❌ Error testing license API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLicenseAPI();
