const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

function generateApiKey() {
  return crypto.randomBytes(32).toString('hex');
}

function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

async function createLocalhostKey() {
  console.log('Creating API key for localhost testing...');
  
  try {
    // Find an active license
    const license = await prisma.license.findFirst({
      where: { isActive: true }
    });
    
    if (!license) {
      console.log('No active license found');
      return;
    }
    
    console.log(`Using license: ${license.id}`);
    
    // Delete any existing localhost keys to avoid conflicts
    await prisma.licenseApiKey.deleteMany({
      where: {
        licenseId: license.id,
        OR: [
          { serverIp: '127.0.0.1' },
          { serverIp: '::1' },
          { serverIp: 'localhost' }
        ]
      }
    });
    
    // Generate a new API key
    const apiKey = generateApiKey();
    const keyHash = hashApiKey(apiKey);
    
    // Create API key for IPv6 localhost (::1) since that's what curl uses
    const newApiKey = await prisma.licenseApiKey.create({
      data: {
        licenseId: license.id,
        keyHash,
        serverIp: '::1', // IPv6 localhost to match curl requests
        serverName: 'Test Server (localhost IPv6)'
      }
    });
    
    console.log(`✅ Created API key for ::1`);
    console.log(`   API Key: ${apiKey}`);
    console.log(`   Key Hash: ${keyHash}`);
    console.log(`   Record ID: ${newApiKey.id}`);
    
    console.log(`\n🧪 Test your API with this curl command:`);
    console.log(`curl -X POST http://localhost:3000/api/license/validate \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{`);
    console.log(`    "apiKey": "${apiKey}",`);
    console.log(`    "licenseType": "headsteal"`);
    console.log(`  }'`);
    
    // Also show what's in the database
    console.log(`\n📊 Current API keys in database:`);
    const allKeys = await prisma.licenseApiKey.findMany({
      include: {
        license: {
          include: {
            user: { select: { email: true } }
          }
        }
      }
    });
    
    for (const key of allKeys) {
      console.log(`- Server IP: ${key.serverIp} | Active: ${key.isActive} | User: ${key.license.user.email}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createLocalhostKey();
