const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

function generateApiKey() {
  return crypto.randomBytes(32).toString('hex');
}

function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

async function createTestKey() {
  console.log('Creating test API key for localhost...');
  
  try {
    // Find an active license
    const license = await prisma.license.findFirst({
      where: { isActive: true }
    });
    
    if (!license) {
      console.log('No active license found');
      return;
    }
    
    console.log(`Using license: ${license.id}`);
    
    // Generate a new API key for localhost testing
    const apiKey = generateApiKey();
    const keyHash = hashApiKey(apiKey);
    
    // Create API key for localhost (both IPv4 and IPv6)
    const localhostIPs = ['127.0.0.1', '::1'];
    
    for (const ip of localhostIPs) {
      try {
        const newApiKey = await prisma.licenseApiKey.create({
          data: {
            licenseId: license.id,
            keyHash,
            serverIp: ip,
            serverName: `Test Server (${ip})`
          }
        });
        
        console.log(`✅ Created API key for ${ip}`);
        console.log(`   API Key: ${apiKey}`);
        console.log(`   Key Hash: ${keyHash}`);
        console.log(`   Record ID: ${newApiKey.id}`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`API key already exists for ${ip}`);
        } else {
          console.error(`Error creating key for ${ip}:`, error.message);
        }
      }
    }
    
    console.log(`\n🧪 Test your API with this curl command:`);
    console.log(`curl -X POST http://localhost:3000/api/license/validate \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{`);
    console.log(`    "apiKey": "${apiKey}",`);
    console.log(`    "licenseType": "headsteal"`);
    console.log(`  }'`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestKey();
