const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

async function debugApiKeys() {
  console.log('Debugging API keys...');
  
  try {
    // Check if we have any licenses
    const licenses = await prisma.license.findMany({
      include: {
        apiKeys: true,
        user: {
          select: { email: true, name: true }
        }
      }
    });
    
    console.log(`Found ${licenses.length} licenses`);
    
    for (const license of licenses) {
      console.log(`\nLicense ID: ${license.id}`);
      console.log(`User: ${license.user?.email || 'Unknown'}`);
      console.log(`Type: ${license.licenseType}`);
      console.log(`Active: ${license.isActive}`);
      console.log(`API Keys: ${license.apiKeys.length}`);
      
      for (const apiKey of license.apiKeys) {
        console.log(`  - Key ID: ${apiKey.id}`);
        console.log(`    Server IP: ${apiKey.serverIp}`);
        console.log(`    Server Name: ${apiKey.serverName || 'None'}`);
        console.log(`    Active: ${apiKey.isActive}`);
        console.log(`    Key Hash: ${apiKey.keyHash}`);
        console.log(`    Created: ${apiKey.createdAt}`);
        console.log(`    Last Used: ${apiKey.lastUsed || 'Never'}`);
      }
    }
    
    // Test the API key you provided
    const testApiKey = "8b609b64f1483d4d63ecd673b9e177ca3c6700fb86126c759991cc6d85b55c19";
    const testHash = hashApiKey(testApiKey);
    
    console.log(`\n--- Testing API Key ---`);
    console.log(`Raw API Key: ${testApiKey}`);
    console.log(`Hashed: ${testHash}`);
    
    // Check if this hash exists in database
    const foundKey = await prisma.licenseApiKey.findFirst({
      where: { keyHash: testHash },
      include: {
        license: {
          include: {
            user: { select: { email: true } }
          }
        }
      }
    });
    
    if (foundKey) {
      console.log(`✅ Found matching API key in database!`);
      console.log(`   Server IP: ${foundKey.serverIp}`);
      console.log(`   Active: ${foundKey.isActive}`);
      console.log(`   License Active: ${foundKey.license.isActive}`);
      console.log(`   License Type: ${foundKey.license.licenseType}`);
    } else {
      console.log(`❌ No matching API key found in database`);
      
      // Check if the raw key exists (in case it wasn't hashed)
      const rawKey = await prisma.licenseApiKey.findFirst({
        where: { keyHash: testApiKey }
      });
      
      if (rawKey) {
        console.log(`⚠️  Found raw API key stored without hashing!`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugApiKeys();
