const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Create sample announcements (you'll need to update these with your actual content)
  const announcements = [
    {
      title: "Welcome to AvehubV1",
      content: "Welcome to our platform! Please update this with your actual announcement content.",
      type: "info",
      isActive: true
    },
    {
      title: "System Maintenance",
      content: "Please update with your actual maintenance announcement.",
      type: "warning", 
      isActive: true
    },
    {
      title: "New Features Available",
      content: "Please update with your actual feature announcement.",
      type: "success",
      isActive: true
    },
    {
      title: "Important Update",
      content: "Please update with your actual update announcement.",
      type: "info",
      isActive: true
    },
    {
      title: "Community Guidelines",
      content: "Please update with your actual guidelines announcement.",
      type: "warning",
      isActive: true
    }
  ];

  console.log('Creating announcements...');
  for (const announcement of announcements) {
    try {
      await prisma.announcement.create({
        data: announcement
      });
      console.log(`Created announcement: ${announcement.title}`);
    } catch (error) {
      console.error(`Failed to create announcement ${announcement.title}:`, error.message);
    }
  }

  // Create an admin user (you'll need to update with your actual admin details)
  console.log('Creating admin user...');
  try {
    const adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>", // UPDATE THIS WITH YOUR ACTUAL ADMIN EMAIL
        name: "Admin User",
        admin: true,
        isDeveloper: true,
        headstealPremium: true
      }
    });
    console.log('Created admin user:', adminUser.email);
  } catch (error) {
    console.error('Failed to create admin user:', error.message);
  }

  console.log('Seeding completed!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
