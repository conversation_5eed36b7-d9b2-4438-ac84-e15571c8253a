const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

async function fixApiKeys() {
  console.log('Fixing API key storage...');
  
  try {
    // The issue: The API key "8b609b64f1483d4d63ecd673b9e177ca3c6700fb86126c759991cc6d85b55c19" 
    // is stored as keyHash, but it should be hashed again
    
    const problematicKey = "8b609b64f1483d4d63ecd673b9e177ca3c6700fb86126c759991cc6d85b55c19";
    const correctHash = hashApiKey(problematicKey);
    
    console.log(`Original key: ${problematicKey}`);
    console.log(`Correct hash: ${correctHash}`);
    
    // Find the record with the problematic key
    const apiKeyRecord = await prisma.licenseApiKey.findFirst({
      where: { keyHash: problematicKey }
    });
    
    if (apiKeyRecord) {
      console.log(`Found problematic record: ${apiKeyRecord.id}`);
      
      // Update it with the correct hash
      await prisma.licenseApiKey.update({
        where: { id: apiKeyRecord.id },
        data: { keyHash: correctHash }
      });
      
      console.log(`✅ Updated API key hash for record ${apiKeyRecord.id}`);
      
      // Verify the fix
      const verification = await prisma.licenseApiKey.findFirst({
        where: { keyHash: correctHash }
      });
      
      if (verification) {
        console.log(`✅ Verification successful - key now properly hashed`);
      } else {
        console.log(`❌ Verification failed`);
      }
    } else {
      console.log(`No problematic record found`);
    }
    
    // Check the other API key too
    const otherKeys = await prisma.licenseApiKey.findMany({
      where: {
        keyHash: {
          not: correctHash
        }
      }
    });
    
    console.log(`\nOther API keys in database: ${otherKeys.length}`);
    for (const key of otherKeys) {
      console.log(`- ${key.keyHash} (Server: ${key.serverIp})`);
      // Check if this looks like a raw key (64 chars, hex)
      if (key.keyHash.length === 64 && /^[a-f0-9]+$/.test(key.keyHash)) {
        console.log(`  ⚠️  This might also be a raw key that needs hashing`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixApiKeys();
